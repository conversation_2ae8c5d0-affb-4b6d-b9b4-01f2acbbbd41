<script lang="ts">
  import { goto } from "$app/navigation";
  import { gameActions, gameState } from "$lib";
  import RoomIdManager from "$lib/components/RoomIdManager.svelte";

  interface Game {
    id: string;
    name: string;
    description: string;
    color: string;
    available: boolean;
  }

  const games: Game[] = [
    {
      id: "finger-frenzy",
      name: "Finger Frenzy",
      description: "Fast-paced tapping game",
      color: "from-red-500 to-orange-500",
      available: true,
    },
    {
      id: "bingo",
      name: "Bingo",
      description: "Classic bingo game",
      color: "from-blue-500 to-purple-500",
      available: true,
    },
    {
      id: "matching-mayhem",
      name: "Matching Mayhem",
      description: "Memory matching game",
      color: "from-green-500 to-teal-500",
      available: true,
    },
    {
      id: "numbers",
      name: "Number Sequence",
      description: "Number pattern recognition",
      color: "from-purple-500 to-pink-500",
      available: true,
    },
    {
      id: "mums-numbers",
      name: "Mums Numbers!",
      description:
        "Draw a continuous path through numbers 1-5 covering all grid cells",
      color: "from-cyan-500 to-blue-500",
      available: true,
    },
  ];

  // let roomIdManager: RoomIdManager;

  // Function to generate JWT token from server
  async function generateDemoJWT(gameId: string): Promise<string> {
    try {
      const serverUrl = "http://localhost:3000"; // TODO: Make this configurable
      const response = await fetch(`${serverUrl}/api/generate-token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          gameId,
          // roomId: roomIdManager?.getRoomId(),
          clientSeed: `1234567890`,
          username: `Player-${Math.floor(Math.random() * 1000)}`,
          // email: `player${Math.floor(Math.random() * 1000)}@example.com`,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate token: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success || !data.token) {
        throw new Error("Invalid response from token generation endpoint");
      }

      console.log("Generated JWT token data:", {
        gameId: data.userData.gameId,
        roomId: data.userData.roomId,
        userId: data.userData.userId,
        username: data.userData.username,
      });

      gameActions.setRoomData(
        data.userData.roomId,
        data.token,
        data.submitScoreId
      );

      return data.token;
    } catch (error) {
      console.error("Error generating JWT token:", error);
      throw error;
    }
  }

  // Function to handle game selection with JWT
  async function handleGameSelect(gameId: string) {
    if (!games.find((g) => g.id === gameId)?.available) {
      console.warn(`Game ${gameId} is not available`);
      return;
    }

    try {
      // Generate JWT token for the selected game
      const token = await generateDemoJWT(gameId);

      console.log(`Generated JWT for ${gameId}:`, token);

      // Navigate to game page with JWT token
      goto(`/game/${gameId}?token=${token}`);
    } catch (error) {
      console.error(`Failed to start game ${gameId}:`, error);
      alert(
        `Failed to start game: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }
</script>

<svelte:head>
  <title>TicTaps Games</title>
  <meta
    name="description"
    content="Select from our collection of exciting mini-games"
  />
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"
>
  <!-- Header -->
  <header class="text-center py-12 px-4">
    <h1
      class="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
    >
      TicTaps Games
    </h1>
  </header>

  <!-- Room ID Manager -->
  <!-- <div class="container mx-auto px-4 mb-8">
    <div class="max-w-md mx-auto">
      <RoomIdManager bind:this={roomIdManager} />
    </div>
  </div> -->

  <!-- Games Grid -->
  <main class="container mx-auto px-4 pb-12">
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-4xl mx-auto"
    >
      {#each games as game}
        <div class="game-card group">
          {#if game.available}
            <button
              on:click={() => handleGameSelect(game.id)}
              class="block h-full w-full p-6 rounded-xl bg-gradient-to-br {game.color} shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-white/10 text-left cursor-pointer"
            >
              <div class="flex items-center justify-between mb-4">
                <div
                  class="px-3 py-1 bg-white/20 rounded-full text-sm font-medium text-white"
                >
                  Available
                </div>
              </div>

              <h2
                class="text-2xl font-bold text-white mb-2 group-hover:text-yellow-200 transition-colors"
              >
                {game.name}
              </h2>

              <p class="text-white/80 text-sm leading-relaxed mb-4">
                {game.description}
              </p>

              <div class="flex items-center text-white/60 text-sm">
                <svg
                  class="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                Play Now
              </div>
            </button>
          {:else}
            <div
              class="block h-full p-6 rounded-xl bg-gradient-to-br from-gray-600 to-gray-700 shadow-lg border border-white/10 opacity-60 cursor-not-allowed"
            >
              <div class="flex items-center justify-between mb-4">
                <div
                  class="px-3 py-1 bg-gray-500/50 rounded-full text-sm font-medium text-gray-300"
                >
                  Coming Soon
                </div>
              </div>

              <h2 class="text-2xl font-bold text-gray-300 mb-2">
                {game.name}
              </h2>

              <p class="text-gray-400 text-sm leading-relaxed mb-4">
                {game.description}
              </p>

              <div class="flex items-center text-gray-500 text-sm">
                <svg
                  class="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                Not Available
              </div>
            </div>
          {/if}
        </div>
      {/each}
    </div>
  </main>
</div>

<style>
  .game-card {
    min-height: 200px;
  }

  .game-card button:focus {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
  }

  @media (max-width: 768px) {
    .game-card {
      min-height: 180px;
    }
  }
</style>
