<script lang="ts">
  import { goto, invalidate } from "$app/navigation";
  import { roomState, roomActions } from "$lib/stores/roomState";
  import { opponentState } from "$lib/stores/opponent";
  import { socketClient } from "$lib/socket";

  interface Props {
    show?: boolean;
    finalScore?: number;
  }

  let { show = false, finalScore = 0 }: Props = $props();

  // Get current player info
  let currentPlayer = $derived(
    $roomState.players.find((p) => p.userId === $roomState.currentUserId)
  );
  let opponent = $derived($opponentState.opponent);
  let isInMultiplayer = $derived(
    $roomState.status !== "not_in_room" && $roomState.players.length > 1
  );

  // Determine winner
  let winner = $derived(
    isInMultiplayer && opponent
      ? finalScore > opponent.score
        ? "player"
        : finalScore < opponent.score
          ? "opponent"
          : "tie"
      : null
  );

  function handleBackToLobby() {
    // Reset room state and go back to lobby
    roomActions.reset();
    window.location.href = "/";
  }

  function handleLeaveRoom() {
    socketClient.leaveRoom();
    roomActions.reset();
    window.location.href = "/";
  }
</script>

{#if show}
  <!-- End Game Overlay with game background -->
  <div class="game-end-overlay">
    <!-- Background with blur effect -->
    <div class="game-background"></div>

    <!-- Main panel with blur and transparency -->
    <div class="game-panel">
      <!-- Blur background for panel -->
      <div class="panel-blur-bg"></div>

      <!-- Main panel content -->
      <div class="panel-content">
        <!-- Game Over Title with glow effect -->
        <div class="game-over-title">
          <h1 class="game-over-text">GAME OVER</h1>
        </div>

        <!-- Winner announcement (only in multiplayer) -->
        {#if isInMultiplayer && winner}
          <div class="winner-section">
            {#if winner === "player"}
              <div class="winner-text victory">YOU WON!</div>
            {:else if winner === "opponent"}
              <div class="winner-text defeat">YOU LOST</div>
            {:else}
              <div class="winner-text tie">IT'S A TIE!</div>
            {/if}
          </div>
        {/if}

        <!-- Players Section (Top-down layout) -->
        <div class="players-section">
          <!-- Current Player -->
          <div class="player-card current-player">
            <div class="player-avatar">
              <div class="avatar-circle">
                {currentPlayer?.displayName?.charAt(0)?.toUpperCase() ||
                  currentPlayer?.name?.charAt(0)?.toUpperCase() ||
                  "P"}
              </div>
            </div>
            <div class="player-info">
              <div class="player-name">
                {currentPlayer?.displayName || currentPlayer?.name || "You"}
              </div>
              <div class="player-label">Your Score</div>
            </div>
            <div class="player-score">{finalScore}</div>
          </div>

          <!-- Opponent (if in multiplayer) -->
          {#if isInMultiplayer && opponent}
            <div class="vs-divider">VS</div>
            <div class="player-card opponent-player">
              <div class="player-avatar">
                <div class="avatar-circle opponent">
                  {opponent.name?.charAt(0)?.toUpperCase() || "O"}
                </div>
              </div>
              <div class="player-info">
                <div class="player-name">{opponent.name || "Opponent"}</div>
                <div class="player-label">Opponent Score</div>
              </div>
              <div class="player-score">{opponent.score}</div>
            </div>
          {/if}
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          {#if isInMultiplayer}
            <!-- Multiplayer actions -->
            <button class="action-btn secondary" onclick={handleLeaveRoom}>
              <span class="btn-text">LEAVE ROOM</span>
            </button>
          {:else}
            <!-- Solo play actions -->
            <button class="action-btn secondary" onclick={handleBackToLobby}>
              <span class="btn-text">BACK TO LOBBY</span>
            </button>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .game-end-overlay {
    position: fixed;
    inset: 0;
    z-index: 3000;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); */
  }

  .game-background {
    position: absolute;
    inset: 0;
    background: black;
    opacity: 0.5;
  }

  .game-panel {
    position: relative;
    width: 80%;
    max-width: 500px;
    /* height: 60%; */
    /* max-height: 600px; */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .panel-blur-bg {
    position: absolute;
    inset: -2px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .panel-content {
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(26, 35, 49, 0.4);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 2rem;
    box-sizing: border-box;
    gap: 1rem;
    overflow-y: auto;
  }

  .game-over-title {
    flex-shrink: 0;
    margin-bottom: 1rem;
  }

  .game-over-text {
    font-family: Arial, sans-serif;
    font-size: clamp(2rem, 8vw, 4rem);
    font-weight: bold;
    margin: 0;
    background: linear-gradient(90deg, #4bffae 0%, #32c4ff 50%, #5c67ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(75, 255, 174, 0.5);
    filter: drop-shadow(0 0 15px rgba(75, 255, 174, 0.3));
  }

  .winner-section {
    text-align: center;
    margin: 1rem 0;
  }

  .winner-text {
    font-family: Arial, sans-serif;
    font-size: clamp(1.5rem, 5vw, 2.5rem);
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  }

  .winner-text.victory {
    color: #4cffae;
    text-shadow: 0 0 20px rgba(76, 255, 174, 0.5);
  }

  .winner-text.defeat {
    color: #ff6b6b;
    text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
  }

  .winner-text.tie {
    color: #ffd93d;
    text-shadow: 0 0 20px rgba(255, 217, 61, 0.5);
  }

  .players-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .player-card {
    width: 100%;
    max-width: 400px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .player-card.current-player {
    border-color: #4cffae;
    box-shadow: 0 0 20px rgba(76, 255, 174, 0.3);
  }

  .player-card.opponent-player {
    border-color: #ff6b6b;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
  }

  .player-avatar {
    flex-shrink: 0;
  }

  .avatar-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4cffae 0%, #32c4ff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Arial, sans-serif;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  }

  .avatar-circle.opponent {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  }

  .player-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .player-name {
    font-family: Arial, sans-serif;
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
  }

  .player-label {
    font-family: Arial, sans-serif;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
  }

  .player-score {
    font-family: Arial, sans-serif;
    font-size: 2rem;
    font-weight: bold;
    background: linear-gradient(135deg, #4cffae 0%, #32c4ff 40%, #5c67ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    -webkit-text-stroke: 1px rgba(255, 255, 255, 0.3);
    flex-shrink: 0;
  }

  .vs-divider {
    font-family: Arial, sans-serif;
    font-size: 1.5rem;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    margin: 0.5rem 0;
  }

  .action-buttons {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    margin-top: auto;
  }

  .action-btn {
    position: relative;
    width: 100%;
    max-width: 300px;
    height: 60px;
    background: none;
    border: 2px solid;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: Arial, sans-serif;
    font-weight: bold;
    font-size: 1rem;
  }

  .action-btn.primary {
    border-color: #4cffae;
    background: linear-gradient(
      135deg,
      rgba(76, 255, 174, 0.2) 0%,
      rgba(50, 196, 255, 0.2) 100%
    );
    color: #4cffae;
  }

  .action-btn.primary:hover {
    background: linear-gradient(
      135deg,
      rgba(76, 255, 174, 0.3) 0%,
      rgba(50, 196, 255, 0.3) 100%
    );
    box-shadow: 0 0 20px rgba(76, 255, 174, 0.4);
    transform: translateY(-2px);
  }

  .action-btn.secondary {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
  }

  .action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
  }

  .action-btn:active {
    transform: translateY(0);
  }

  .btn-text {
    position: relative;
    z-index: 1;
  }

  .waiting-text {
    font-family: Arial, sans-serif;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    font-style: italic;
    margin: 1rem 0;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .game-panel {
      width: 90%;
      height: 80%;
    }

    .panel-content {
      padding: 1.5rem;
      gap: 0.75rem;
    }

    .player-card {
      padding: 1rem;
    }

    .avatar-circle {
      width: 50px;
      height: 50px;
      font-size: 1.2rem;
    }

    .player-score {
      font-size: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .panel-content {
      padding: 1rem;
      gap: 0rem;
    }

    .player-card {
      padding: 0.75rem;
      gap: 0.75rem;
    }

    .avatar-circle {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }

    .player-name {
      font-size: 1rem;
    }

    .player-score {
      font-size: 1.25rem;
    }

    .action-btn {
      height: 50px;
      font-size: 0.9rem;
    }
  }
</style>
