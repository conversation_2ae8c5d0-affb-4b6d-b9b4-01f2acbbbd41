export type GameType = 'default'|'number-connect' | 'finger-frenzy' | 'bingo' | 'matching-mayhem' | 'numbers' | 'mums-numbers';

export interface GameAction {
  type: string;
  data: Record<string, unknown>;
  timestamp: number;
}

export interface GameScore {
  playerId: string;
  score: number;
  gameType: GameType;
  timestamp: number;
}

export interface GameState {
  gameType: GameType;
  status: 'waiting' | 'starting' | 'active' | 'paused' | 'ended';
  startTime?: number;
  score: number;
  scoreAry: number[];
  lives: number;
}

export interface GameResults {
  gameType: GameType;
  score:  number;
  endReason: EndReason;
}

/**
 * Game initialization result
 */
export interface GameInitResult {
  success: boolean;
  gameState?: GameState;
  message?: string;
}

/**
 * Reasons for ending a game
 */
export type EndReason = 'completed'| 'no_lives' | 'timeout' | 'disconnection' | 'manual';
// 'timeout' | 'no_lives' | 'manual';

/**
 * Socket event data structures
 */
export interface GameStartData {
  roomId: string;
  gameId: string;
  submitScoreId?: string;
}

export interface GameEndData {
  roomId: string;
  gameId: string;
  submitScoreId?: string;
  reason?: EndReason;
}

export interface GameActionData {
  roomId: string;
  gameId: string;
  action: {
    type: string;
    data: Record<string, unknown>;
  };
}

/**
 * Base interface for game action results
 * All game-specific action results should extend this interface
 */
export interface BaseActionResult {
  success: boolean;
  isCorrect: boolean;
  points: number;
  newScore: number;
  newLives: number;
  gameEnded: boolean;
}

/**
 * User data extracted from JWT token
 */
export interface AuthenticatedUser {
  userId: string;
  username: string;
  gameId: string;
  roomId: string;
  scoreSubmitId: string;
  authToken: string;
  // PRNG: optional client-provided seed for provably fair RNG
  clientSeed?: string;
  exp?: number;
  iat?: number;
}

/**
 * Enhanced session data with authenticated user information
 */
export interface UserSession {
  user: AuthenticatedUser;
  // Each user keeps their own game state for the room; may be null until initialized
  gameState: GameState | null;
  socketId: string;
  connectedAt: Date;
  lastActivity: Date;
  // PRNG: per-user cursor to advance deterministic RNG within a room session
  prngCursor?: number;
}

/**
 * Room session data with user information
 */
export interface RoomSession {
  roomId: string;
  gameId: string;
  users: Map<string, UserSession>;
  createdAt: Date;
  lastActivity: Date;
  // PRNG: room-scoped server seed (shared by all players) and a nonce
  serverSeed?: string;
  nonce?: number;
}